{"ast": null, "code": "import axiosInstance from \"./axiosInstance\";\nexport const login = async data => {\n  return await axiosInstance.post(\"/auth/login\", data);\n};\nexport const getCurrentUser = async () => {\n  return await axiosInstance.get(\"/auth/me\");\n};\nexport const forgotPassword = async email => {\n  return await axiosInstance.post(\"/auth/forget-password\", {\n    email\n  });\n};\nexport const resetPassword = async (data, token) => {\n  return await axiosInstance.post(`/auth/reset-password/${token}`, data);\n};\nexport const resetPasswordLink = async email => {\n  return await axiosInstance.post(\"/auth/reset-password-email\", {\n    email\n  });\n};", "map": {"version": 3, "names": ["axiosInstance", "login", "data", "post", "getCurrentUser", "get", "forgotPassword", "email", "resetPassword", "token", "resetPasswordLink"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/services/auth.js"], "sourcesContent": ["import axiosInstance from \"./axiosInstance\";\r\n\r\nexport const login = async (data) => {\r\n  return await axiosInstance.post(\"/auth/login\", data);\r\n};\r\n\r\nexport const getCurrentUser = async () => {\r\n  return await axiosInstance.get(\"/auth/me\");\r\n};\r\n\r\nexport const forgotPassword = async (email) => {\r\n  return await axiosInstance.post(\"/auth/forget-password\", { email });\r\n};\r\n\r\nexport const resetPassword = async (data, token) => {\r\n  return await axiosInstance.post(`/auth/reset-password/${token}`, data);\r\n};\r\n\r\nexport const resetPasswordLink = async (email) => {\r\n  return await axiosInstance.post(\"/auth/reset-password-email\", { email });\r\n};\r\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAE3C,OAAO,MAAMC,KAAK,GAAG,MAAOC,IAAI,IAAK;EACnC,OAAO,MAAMF,aAAa,CAACG,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;AACtD,CAAC;AAED,OAAO,MAAME,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,OAAO,MAAMJ,aAAa,CAACK,GAAG,CAAC,UAAU,CAAC;AAC5C,CAAC;AAED,OAAO,MAAMC,cAAc,GAAG,MAAOC,KAAK,IAAK;EAC7C,OAAO,MAAMP,aAAa,CAACG,IAAI,CAAC,uBAAuB,EAAE;IAAEI;EAAM,CAAC,CAAC;AACrE,CAAC;AAED,OAAO,MAAMC,aAAa,GAAG,MAAAA,CAAON,IAAI,EAAEO,KAAK,KAAK;EAClD,OAAO,MAAMT,aAAa,CAACG,IAAI,CAAC,wBAAwBM,KAAK,EAAE,EAAEP,IAAI,CAAC;AACxE,CAAC;AAED,OAAO,MAAMQ,iBAAiB,GAAG,MAAOH,KAAK,IAAK;EAChD,OAAO,MAAMP,aAAa,CAACG,IAAI,CAAC,4BAA4B,EAAE;IAAEI;EAAM,CAAC,CAAC;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}