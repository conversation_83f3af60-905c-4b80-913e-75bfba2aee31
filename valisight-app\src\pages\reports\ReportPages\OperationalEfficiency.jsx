import React, { useEffect, useRef } from 'react';
import ApexCharts from 'apexcharts';

const OperationalEfficiencyDashboard = ({
  headerTextStyle = {},
  headingTextStyle = {},
  subHeadingTextStyle = {},
  contentTextStyle = {},
  operationalData = null
}) => {
  console.log('OperationalEfficiency - operationalData:', operationalData);
  const salesOutstandingRef = useRef(null);
  const payablesOutstandingRef = useRef(null);
  const inventoryOutstandingRef = useRef(null);
  const cashConversionRef = useRef(null);
  const fixedAssetTurnoverRef = useRef(null);

  // Enhanced data validation function - more lenient approach like FiscalYear
  const isDataLoaded = () => {
    if (!operationalData) {
      console.log('OperationalEfficiency - No operationalData provided');
      return false;
    }
    
    console.log('OperationalEfficiency - operationalData keys:', Object.keys(operationalData));
    console.log('OperationalEfficiency - Full operationalData:', operationalData);
    
    // Check if at least one of the required data arrays exists and has content
    const hasSalesOutstandingData = operationalData.daysSalesAROutstanding && 
                                   Array.isArray(operationalData.daysSalesAROutstanding) && 
                                   operationalData.daysSalesAROutstanding.length > 0;
    
    const hasPayablesOutstandingData = operationalData.daysSalesAPOutstanding && 
                                      Array.isArray(operationalData.daysSalesAPOutstanding) && 
                                      operationalData.daysSalesAPOutstanding.length > 0;
    
    const hasInventoryOutstandingData = operationalData.daysInventoryOutstanding && 
                                       Array.isArray(operationalData.daysInventoryOutstanding) && 
                                       operationalData.daysInventoryOutstanding.length > 0;
    
    const hasCashConversionData = operationalData.cashConversionCycle && 
                                 Array.isArray(operationalData.cashConversionCycle) && 
                                 operationalData.cashConversionCycle.length > 0;
    
    const hasFixedAssetTurnoverData = operationalData.fixedAssetTurnover && 
                                     Array.isArray(operationalData.fixedAssetTurnover) && 
                                     operationalData.fixedAssetTurnover.length > 0;

    console.log('OperationalEfficiency - Data validation:', {
      hasSalesOutstandingData,
      hasPayablesOutstandingData,
      hasInventoryOutstandingData,
      hasCashConversionData,
      hasFixedAssetTurnoverData,
      salesOutstandingLength: operationalData.daysSalesAROutstanding?.length || 0,
      payablesOutstandingLength: operationalData.daysSalesAPOutstanding?.length || 0,
      inventoryOutstandingLength: operationalData.daysInventoryOutstanding?.length || 0,
      cashConversionLength: operationalData.cashConversionCycle?.length || 0,
      fixedAssetTurnoverLength: operationalData.fixedAssetTurnover?.length || 0
    });

    // Log sample data for debugging
    if (hasSalesOutstandingData) {
      console.log('OperationalEfficiency - Sample sales outstanding data:', operationalData.daysSalesAROutstanding[0]);
    }
    if (hasPayablesOutstandingData) {
      console.log('OperationalEfficiency - Sample payables outstanding data:', operationalData.daysSalesAPOutstanding[0]);
    }

    // Return true if we have at least one data array (most important check)
    // We'll handle missing other data gracefully in the charts
    return hasSalesOutstandingData || hasPayablesOutstandingData || hasInventoryOutstandingData || 
           hasCashConversionData || hasFixedAssetTurnoverData;
  };

  // Function to check if we have any meaningful data (not all zeros) - like FiscalYear
  const hasAnyUsableData = () => {
    if (!operationalData) {
      console.log('OperationalEfficiency - No operational data for usability check');
      return false;
    }

    const categories = operationalData.daysSalesAROutstanding?.map(item => 
      formatMonthYear(item.year, item.month)
    ) || [];

    const daysSalesOutstandingData = operationalData.daysSalesAROutstanding?.map(item => {
      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysPayablesOutstandingData = operationalData.daysSalesAPOutstanding?.map(item => {
      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysInventoryOutstandingData = operationalData.daysInventoryOutstanding?.map(item => {
      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const cashConversionCycleData = operationalData.cashConversionCycle?.map(item => {
      const value = parseFloat(item.CCC || item.ccc);
      return isNaN(value) || value === null ? 0 : value;
    }) || [];

    const fixedAssetTurnoverData = operationalData.fixedAssetTurnover?.map(item => 
      parseFloat(item.fat) || 0
    ) || [];

    // Check if any data has meaningful values
    const hasMeaningfulSalesData = daysSalesOutstandingData.some(val => val > 0);
    const hasMeaningfulPayablesData = daysPayablesOutstandingData.some(val => val > 0);
    const hasMeaningfulInventoryData = daysInventoryOutstandingData.some(val => val > 0);
    const hasMeaningfulCashConversionData = cashConversionCycleData.some(val => val !== 0 && val !== null);
    const hasMeaningfulFixedAssetData = fixedAssetTurnoverData.some(val => val > 0);

    console.log('OperationalEfficiency - Has meaningful data:', {
      hasMeaningfulSalesData,
      hasMeaningfulPayablesData,
      hasMeaningfulInventoryData,
      hasMeaningfulCashConversionData,
      hasMeaningfulFixedAssetData,
      categoriesLength: categories.length
    });

    return categories.length > 0 && (hasMeaningfulSalesData || hasMeaningfulPayablesData || 
           hasMeaningfulInventoryData || hasMeaningfulCashConversionData || hasMeaningfulFixedAssetData);
  };

  useEffect(() => {
    if (isDataLoaded()) {
      // Clear charts first
      [salesOutstandingRef, payablesOutstandingRef, inventoryOutstandingRef, cashConversionRef, fixedAssetTurnoverRef].forEach((ref) => {
        if (ref.current) {
          ref.current.innerHTML = "";
        }
      });
      // Initialize charts with new data
      initializeCharts();
    }
  }, [operationalData]);

  const formatMonthYear = (year, month) => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[month - 1]} ${String(year).slice(-2)}`;
  };

  const initializeCharts = () => {
    if (!operationalData) return;

    console.log('OperationalEfficiency - Initializing charts with data:', operationalData);
    
    // Try to find the correct data structure
    let dataToUse = operationalData;
    
    // Check if data is nested under different possible paths
    if (operationalData.operationalEfficiency) {
      dataToUse = operationalData.operationalEfficiency;
      console.log('OperationalEfficiency - Using nested operationalEfficiency data');
    } else if (operationalData.operational) {
      dataToUse = operationalData.operational;
      console.log('OperationalEfficiency - Using nested operational data');
    } else if (operationalData.data) {
      dataToUse = operationalData.data;
      console.log('OperationalEfficiency - Using nested data');
    } else if (operationalData.reportData) {
      dataToUse = operationalData.reportData;
      console.log('OperationalEfficiency - Using nested reportData');
    }
    
    console.log('OperationalEfficiency - Final data structure to use:', dataToUse);

    // Prepare data from API response - using correct property names from your JSON
    const categories = dataToUse.daysSalesAROutstanding?.map(item => 
      formatMonthYear(item.year, item.month)
    ) || [];

    const daysSalesOutstandingData = dataToUse.daysSalesAROutstanding?.map(item => {
      const value = item.dso || item.days_sales_outstanding || item.daysSalesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysPayablesOutstandingData = dataToUse.daysSalesAPOutstanding?.map(item => {
      const value = item.dpo || item.days_payables_outstanding || item.daysPayablesOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const daysInventoryOutstandingData = dataToUse.daysInventoryOutstanding?.map(item => {
      const value = item.dio || item.days_inventory_outstanding || item.daysInventoryOutstanding || 0;
      return parseFloat(value) || 0;
    }) || [];

    const cashConversionCycleData = dataToUse.cashConversionCycle?.map(item => {
      const value = parseFloat(item.CCC || item.ccc);
      return isNaN(value) || value === null ? 0 : value;
    }) || [];

    const fixedAssetTurnoverData = dataToUse.fixedAssetTurnover?.map(item => 
      parseFloat(item.fat) || 0
    ) || [];

    console.log('OperationalEfficiency - Processed data:', {
      categoriesLength: categories.length,
      salesOutstandingLength: daysSalesOutstandingData.length,
      salesOutstandingData: daysSalesOutstandingData,
      payablesOutstandingLength: daysPayablesOutstandingData.length,
      payablesOutstandingData: daysPayablesOutstandingData,
      inventoryOutstandingLength: daysInventoryOutstandingData.length,
      inventoryOutstandingData: daysInventoryOutstandingData,
      cashConversionLength: cashConversionCycleData.length,
      cashConversionData: cashConversionCycleData,
      fixedAssetTurnoverLength: fixedAssetTurnoverData.length,
      fixedAssetTurnoverData: fixedAssetTurnoverData
    });

    // Color scheme
    const colors = {
      salesOutstanding: '#2d6a9b',
      payablesOutstanding: '#565aa4',
      inventoryOutstanding: '#2a689a',
      cashConversion: '#ff6b47',
      fixedAssetTurnover: '#2d6a9b'
    };

    // 1. Days Sales (A/R) Outstanding Chart
    const salesOutstandingOptions = {
      series: [{
        name: 'Days Sales Outstanding',
        data: daysSalesOutstandingData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.salesOutstanding]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.salesOutstanding, opacity: 0.4 },
            { offset: 100, color: colors.salesOutstanding, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.salesOutstanding],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: { 
            colors: '#666', 
            fontSize: '14px' 
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.salesOutstanding],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: { 
        show: false 
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 2. Days Payables (AP) Outstanding Chart
    const payablesOutstandingOptions = {
      series: [{
        name: 'Days Payables Outstanding',
        data: daysPayablesOutstandingData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        parentHeightOffset: 0,
        sparkline: {
          enabled: false
        },
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.payablesOutstanding]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.payablesOutstanding, opacity: 0.4 },
            { offset: 100, color: colors.payablesOutstanding, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.payablesOutstanding],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: { 
            colors: '#666', 
            fontSize: '14px' 
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.payablesOutstanding],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: { 
        show: false 
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 3. Days Inventory Outstanding Chart (Bar Chart)
    const inventoryOutstandingOptions = {
      series: [{
        name: 'Days Inventory Outstanding',
        data: daysInventoryOutstandingData
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: { show: false },
        background: 'transparent'
      },
      plotOptions: {
        bar: {
          columnWidth: '40%',
          dataLabels: {
            position: 'top'
          }
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -20,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: { 
            colors: '#666', 
            fontSize: '14px' 
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.inventoryOutstanding],
      grid: {
        show: false,
        padding: {
          left: 10,
          right: 10,
          top: 25,
          bottom: 0
        }
      },
      legend: { 
        show: false 
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 4. Cash Conversion Cycle Chart
    const cashConversionOptions = {
      series: [{
        name: 'Cash Conversion Cycle',
        data: cashConversionCycleData
      }],
      chart: {
        type: 'line',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return Math.round(val);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        width: 2,
        colors: [colors.cashConversion]
      },
      markers: {
        size: 4,
        colors: [colors.cashConversion],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: { 
            colors: '#666', 
            fontSize: '14px' 
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.cashConversion],
      grid: {
        show: false,
        padding: {
          left: 25,
          right: 25,
          top: 20,
          bottom: 0
        }
      },
      legend: { 
        show: false 
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return Math.round(val) + ' days';
          }
        }
      }
    };

    // 5. Fixed Asset Turnover Chart
    const fixedAssetTurnoverOptions = {
      series: [{
        name: 'Fixed Asset Turnover',
        data: fixedAssetTurnoverData
      }],
      chart: {
        type: 'area',
        height: 200,
        toolbar: { show: false },
        background: 'transparent',
        zoom : {
          enabled : false
        }
      },
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return val.toFixed(2);
        },
        style: {
          fontSize: '14px',
          colors: ['#333'],
          fontWeight: '500'
        },
        offsetY: -10,
        background: {
          enabled: false
        },
        dropShadow: {
          enabled: false
        }
      },
      stroke: {
        curve: 'smooth',
        width: 2,
        colors: [colors.fixedAssetTurnover]
      },
      fill: {
        type: 'gradient',
        gradient: {
          shadeIntensity: 1,
          type: 'vertical',
          colorStops: [
            { offset: 0, color: colors.fixedAssetTurnover, opacity: 0.4 },
            { offset: 100, color: colors.fixedAssetTurnover, opacity: 0.1 }
          ]
        }
      },
      markers: {
        size: 4,
        colors: [colors.fixedAssetTurnover],
        strokeColors: '#fff',
        strokeWidth: 2,
        hover: {
          size: 6
        }
      },
      xaxis: {
        categories: categories,
        labels: {
          style: { 
            colors: '#666', 
            fontSize: '14px' 
          }
        },
        axisBorder: { show: false },
        axisTicks: { show: false }
      },
      yaxis: {
        show: false
      },
      colors: [colors.fixedAssetTurnover],
      grid: {
        show: false,
        padding: {
          left: 15,
          right: 15,
          top: 20,
          bottom: 0
        }
      },
      legend: { 
        show: false 
      },
      tooltip: {
        y: {
          formatter: function(val) {
            return val.toFixed(2);
          }
        }
      }
    };

    // Clear existing charts before rendering new ones
    const clearAndRenderChart = (ref, options, chartName) => {
      if (ref.current) {
        // Clear any existing chart
        ref.current.innerHTML = '';
        
        // Wait a tick before rendering to ensure DOM is cleared
        setTimeout(() => {
          if (ref.current) {
            try {
              console.log(`OperationalEfficiency - Rendering ${chartName} chart`);
              const chart = new ApexCharts(ref.current, options);
              chart.render();

              // Store chart instances globally for export
              if (chartName === "Days Sales Outstanding") {
                window.salesOutstandingChart = chart;
              } else if (chartName === "Days Payables Outstanding") {
                window.payablesOutstandingChart = chart;
              } else if (chartName === "Days Inventory Outstanding") {
                window.inventoryOutstandingChart = chart;
              } else if (chartName === "Cash Conversion Cycle") {
                window.cashConversionChart = chart;
              } else if (chartName === "Fixed Asset Turnover") {
                window.fixedAssetTurnoverChart = chart;
              }
            } catch (error) {
              console.error(`OperationalEfficiency - Error rendering ${chartName} chart:`, error);
              // Show error message in chart container
              ref.current.innerHTML = `<div class="flex items-center justify-center h-48 text-gray-500">Error loading ${chartName} chart</div>`;
            }
          }
        }, 10);
      }
    };

    // Only render charts if we have data for them and the data has meaningful values
    console.log('OperationalEfficiency - Chart rendering check:', {
      salesDataLength: daysSalesOutstandingData.length,
      salesDataHasValues: daysSalesOutstandingData.some(val => val > 0),
      salesData: daysSalesOutstandingData,
      payablesDataLength: daysPayablesOutstandingData.length,
      payablesDataHasValues: daysPayablesOutstandingData.some(val => val > 0),
      payablesData: daysPayablesOutstandingData
    });

    if (daysSalesOutstandingData.length > 0 && daysSalesOutstandingData.some(val => val > 0)) {
      clearAndRenderChart(salesOutstandingRef, salesOutstandingOptions, 'Days Sales Outstanding');
    } else if (salesOutstandingRef.current) {
      salesOutstandingRef.current.innerHTML = '<div class="flex items-center justify-center h-48 text-gray-500">No meaningful sales outstanding data available</div>';
    }

    if (daysPayablesOutstandingData.length > 0 && daysPayablesOutstandingData.some(val => val > 0)) {
      clearAndRenderChart(payablesOutstandingRef, payablesOutstandingOptions, 'Days Payables Outstanding');
    } else if (payablesOutstandingRef.current) {
      payablesOutstandingRef.current.innerHTML = '<div class="flex items-center justify-center h-48 text-gray-500">No meaningful payables outstanding data available</div>';
    }

    if (daysInventoryOutstandingData.length > 0 && daysInventoryOutstandingData.some(val => val > 0)) {
      clearAndRenderChart(inventoryOutstandingRef, inventoryOutstandingOptions, 'Days Inventory Outstanding');
    } else if (inventoryOutstandingRef.current) {
      inventoryOutstandingRef.current.innerHTML = '<div class="flex items-center justify-center h-64 text-gray-500">No meaningful inventory outstanding data available</div>';
    }

    // Only render cash conversion cycle chart if there's meaningful data
    if (cashConversionCycleData.length > 0 && cashConversionCycleData.some(val => val != null && val !== 0)) {
      clearAndRenderChart(cashConversionRef, cashConversionOptions, 'Cash Conversion Cycle');
    } else if (cashConversionRef.current) {
      // Show "No data available" message
      cashConversionRef.current.innerHTML = '<div class="flex items-center justify-center h-48 text-gray-500">No meaningful cash conversion cycle data available</div>';
    }
    
    if (fixedAssetTurnoverData.length > 0 && fixedAssetTurnoverData.some(val => val > 0)) {
      clearAndRenderChart(fixedAssetTurnoverRef, fixedAssetTurnoverOptions, 'Fixed Asset Turnover');
    } else if (fixedAssetTurnoverRef.current) {
      fixedAssetTurnoverRef.current.innerHTML = '<div class="flex items-center justify-center h-48 text-gray-500">No meaningful fixed asset turnover data available</div>';
    }
  };

  const formatHeaderPeriod = (startYear, startMonth) => {
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];

    if (!startYear || !startMonth) {
      return " "; // fallback
    }

    const startMonthName = monthNames[startMonth - 1]; // Convert 1-based to 0-based index

    return `${startMonthName} ${startYear}`;
  };

     const formatCompanyName = (companyName) => {
    if (!companyName) return '';
    
    if (companyName.length > 15) {
      return companyName.substring(0, 15) + '...';
    }
    
    return companyName;
  };

  // No usable data component (same pattern as FiscalYear)
  // const NoDataComponent = () => (
  //   <div className="p-5">
  //     <div className="max-w-6xl  mx-auto bg-white flex flex-col shadow p-10 mb-2">
  //      <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
  //         <h1
  //           className="text-4xl font-bold text-gray-800 m-0"
  //           style={headerTextStyle}
  //         >
  //           Operational Efficiency
  //         </h1>
  //         <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
  //           {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | Acme Print
  //         </p>
  //       </div>

  //       <div className="flex items-center justify-center h-64">
  //         <div className="text-center">
  //           <div className="text-xl text-gray-600 mb-2">
  //             No operational efficiency data available
  //           </div>
  //           <div className="text-sm text-gray-500">
  //             Please check your data source.
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   </div>
  // );

  // // Check if we have meaningful data after validation passes
  // if (!hasAnyUsableData()) {
  //   return <NoDataComponent />;
  // }

  return (
    <div className="min-h-screen p-5">
      {/* Single Main Container - Same structure as ExpenseSummary */}
      <div className="max-w-6xl h-[420mm] mx-auto bg-white flex flex-col gap-14 p-10 mb-8">

        {/* Header Section */}
       <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Operational Efficiency
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}
          </p>
        </div>

        {/* Days Sales (A/R) Outstanding */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Days Sales (A/R) Outstanding
          </div>
          <div ref={salesOutstandingRef}></div>
        </div>

        {/* Days Payables (AP) Outstanding */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Days Payables (AP) Outstanding
          </div>
          <div ref={payablesOutstandingRef}></div>
          <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
            <div
                className="text-teal-600 text-2xl mb-2"
                style={{...subHeadingTextStyle, fontWeight: 'lighter'}}
            >
              Days AR Outstanding & Days AP Outstanding
            </div>
            <div style={contentTextStyle}>
              Average number of days it takes customers to pay for invoices/ average number of days it takes a company to pay its suppliers.
            </div>
          </div>
        </div>

        {/* Days Inventory Outstanding */}
        <div className="bg-white p-6 border-b-4 mb-24 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Days Inventory Outstanding
          </div>
          <div ref={inventoryOutstandingRef} className="mb-5"></div>
        </div>

      </div>

      <div className="max-w-6xl h-[400mm] mx-auto bg-white flex flex-col gap-10 p-10 mb-8">
        {/* Header Section */}
        <div className="component-header flex items-center justify-between gap-4 mb-8 border-b-4 border-blue-900 pb-2">
          <h1
            className="text-4xl font-bold text-gray-800 m-0"
            style={headerTextStyle}
          >
            Operational Efficiency
          </h1>
          <p className="text-lg text-gray-600 m-0" style={subHeadingTextStyle}>
            {formatHeaderPeriod(operationalData?.FYStartYear, operationalData?.FYStartMonth)} | {formatCompanyName(operationalData?.companyName)}
          </p>
        </div>

        {/* Cash Conversion Cycle */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Cash Conversion Cycle
          </div>
          <div ref={cashConversionRef}></div>
          <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
            <div
              className="text-teal-600 text-2xl"
              style={{...subHeadingTextStyle, fontWeight: 'lighter'}}
            >
              Cash Conversion Cycle (CCC)
            </div>
            <div style={contentTextStyle}>
              The time it takes a company to convert the money spent on inventory or production back into cash by selling its goods or services. A shorter CCC is better because it means less time that money is tied up in inventory or accounts receivable.
            </div>
          </div>
        </div>

        {/* Fixed Asset Turnover */}
        <div className="bg-white p-6 border-b-4 border-blue-900">
          <div
            className="text-2xl font-semibold text-teal-600 mb-5"
            style={subHeadingTextStyle}
          >
            Fixed Asset Turnover
          </div>
          <div ref={fixedAssetTurnoverRef}></div>
          <div className="mt-5 text-xl text-gray-600 leading-relaxed rounded-lg pb-5">
            <div
                className="text-teal-600 text-2xl"
                style={{...subHeadingTextStyle, fontWeight: 'lighter'}}
            >
              Fixed Asset Turnover (FAT)
            </div>
            <div style={contentTextStyle}>
              The ratio of a company's net sales to its average fixed assets over a specific period, usually a year. A higher ratio indicates that a company is using its fixed assets more efficiently, while a lower ratio suggests underutilization.
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default OperationalEfficiencyDashboard;