{"ast": null, "code": "import axiosInstance from \"./axiosInstance\";\nexport const getContentSettings = async (companyId, reportType = 'DEEPSIGHT') => {\n  return await axiosInstance.get(`/content-settings/company/${companyId}/${reportType}`);\n};\nexport const checkQBOConnectionStatus = async companyId => {\n  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);\n};\nexport const getTemplateSettings = async () => {\n  return await axiosInstance.get('/template-settings');\n};\nexport const getGlobalSettings = async (reportType = 'DEEPSIGHT') => {\n  return await axiosInstance.get(`/template-settings/global?reportType=${reportType}`);\n};\nexport const updateTemplateSettings = async settings => {\n  const payload = {\n    settings: settings\n  };\n  return await axiosInstance.put('/template-settings', payload);\n};\nexport const generateReportCalculation = async (companyId, reportId) => {\n  return await axiosInstance.get(`/report/${companyId}/generate-calculation/${reportId}`);\n};\nexport const uploadChartsToS3 = async (charts, companyId, reportId, dateRequested) => {\n  const payload = {\n    charts: charts,\n    companyId: parseInt(companyId),\n    reportId: reportId,\n    dateRequested: dateRequested\n  };\n  return await axiosInstance.post('/charts/upload-multiple', payload);\n};", "map": {"version": 3, "names": ["axiosInstance", "getContentSettings", "companyId", "reportType", "get", "checkQBOConnectionStatus", "getTemplateSettings", "getGlobalSettings", "updateTemplateSettings", "settings", "payload", "put", "generateReportCalculation", "reportId", "uploadChartsToS3", "charts", "dateRequested", "parseInt", "post"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/services/customizeReportService.js"], "sourcesContent": ["import axiosInstance from \"./axiosInstance\";\n\nexport const getContentSettings = async (companyId, reportType = 'DEEPSIGHT') => {\n  return await axiosInstance.get(`/content-settings/company/${companyId}/${reportType}`);\n};\n\nexport const checkQBOConnectionStatus = async (companyId) => {\n  return await axiosInstance.get(`/qbo/status?companyId=${companyId}`);\n};\n\nexport const getTemplateSettings = async () => {\n  return await axiosInstance.get('/template-settings');\n};\n\nexport const getGlobalSettings = async (reportType = 'DEEPSIGHT') => {\n  return await axiosInstance.get(`/template-settings/global?reportType=${reportType}`);\n};\n\nexport const updateTemplateSettings = async (settings) => {\n  const payload = {\n    settings: settings\n  };\n  return await axiosInstance.put('/template-settings', payload);\n};\n\nexport const generateReportCalculation = async (companyId, reportId) => {\n  return await axiosInstance.get(`/report/${companyId}/generate-calculation/${reportId}`);\n};\n\nexport const uploadChartsToS3 = async (charts, companyId, reportId, dateRequested) => {\n  const payload = {\n    charts: charts,\n    companyId: parseInt(companyId),\n    reportId: reportId,\n    dateRequested: dateRequested\n  };\n  return await axiosInstance.post('/charts/upload-multiple', payload);\n};\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAE3C,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,UAAU,GAAG,WAAW,KAAK;EAC/E,OAAO,MAAMH,aAAa,CAACI,GAAG,CAAC,6BAA6BF,SAAS,IAAIC,UAAU,EAAE,CAAC;AACxF,CAAC;AAED,OAAO,MAAME,wBAAwB,GAAG,MAAOH,SAAS,IAAK;EAC3D,OAAO,MAAMF,aAAa,CAACI,GAAG,CAAC,yBAAyBF,SAAS,EAAE,CAAC;AACtE,CAAC;AAED,OAAO,MAAMI,mBAAmB,GAAG,MAAAA,CAAA,KAAY;EAC7C,OAAO,MAAMN,aAAa,CAACI,GAAG,CAAC,oBAAoB,CAAC;AACtD,CAAC;AAED,OAAO,MAAMG,iBAAiB,GAAG,MAAAA,CAAOJ,UAAU,GAAG,WAAW,KAAK;EACnE,OAAO,MAAMH,aAAa,CAACI,GAAG,CAAC,wCAAwCD,UAAU,EAAE,CAAC;AACtF,CAAC;AAED,OAAO,MAAMK,sBAAsB,GAAG,MAAOC,QAAQ,IAAK;EACxD,MAAMC,OAAO,GAAG;IACdD,QAAQ,EAAEA;EACZ,CAAC;EACD,OAAO,MAAMT,aAAa,CAACW,GAAG,CAAC,oBAAoB,EAAED,OAAO,CAAC;AAC/D,CAAC;AAED,OAAO,MAAME,yBAAyB,GAAG,MAAAA,CAAOV,SAAS,EAAEW,QAAQ,KAAK;EACtE,OAAO,MAAMb,aAAa,CAACI,GAAG,CAAC,WAAWF,SAAS,yBAAyBW,QAAQ,EAAE,CAAC;AACzF,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAEb,SAAS,EAAEW,QAAQ,EAAEG,aAAa,KAAK;EACpF,MAAMN,OAAO,GAAG;IACdK,MAAM,EAAEA,MAAM;IACdb,SAAS,EAAEe,QAAQ,CAACf,SAAS,CAAC;IAC9BW,QAAQ,EAAEA,QAAQ;IAClBG,aAAa,EAAEA;EACjB,CAAC;EACD,OAAO,MAAMhB,aAAa,CAACkB,IAAI,CAAC,yBAAyB,EAAER,OAAO,CAAC;AACrE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}