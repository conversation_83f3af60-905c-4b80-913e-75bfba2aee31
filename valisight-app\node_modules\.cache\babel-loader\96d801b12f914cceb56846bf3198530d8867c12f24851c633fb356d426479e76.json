{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\pages\\\\QuickBooksConnect\\\\QuickBooksConnect.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from \"react\";\nimport { Box, Container, Card, Typography, Button, Stack, Chip, CircularProgress, Fade, Paper, Divider, Alert, AlertTitle, LinearProgress, Grid, List, ListItem, ListItemIcon, ListItemText, Stepper, Step, StepLabel, StepContent } from \"@mui/material\";\nimport { CheckCircle as CheckCircleIcon, Error as ErrorIcon, Refresh as RefreshIcon, AccountBalance as AccountBalanceIcon, Security as SecurityIcon, Sync as SyncIcon, TrendingUp as TrendingUpIcon, Assessment as AssessmentIcon, CloudSync as CloudSyncIcon, Speed as SpeedIcon, Shield as ShieldIcon, Analytics as AnalyticsIcon } from \"@mui/icons-material\";\nimport { useParams, useNavigate } from \"react-router-dom\";\nimport Swal from \"sweetalert2\";\nimport { getOneById } from \"../../services/company\";\nimport { connectQBO, disconnectQBO, getQBOStatus } from \"../../services/qbo\";\nimport qboButton from \"../../assets/C2QB_green_btn_med_default.svg\";\nimport qboButtonHover from \"../../assets/C2QB_green_btn_med_hover.svg\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickBooksConnect = () => {\n  _s();\n  const {\n    companyId\n  } = useParams();\n  const navigate = useNavigate();\n  const [company, setCompany] = useState(null);\n  const [qboConnected, setQboConnected] = useState(false);\n  const [qboLoading, setQboLoading] = useState(false);\n  const [qboStatus, setQboStatus] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [connectionProgress, setConnectionProgress] = useState(0);\n  const [activeStep, setActiveStep] = useState(0);\n  const connectionSteps = [{\n    label: \"Authorize Connection\",\n    description: 'Click \"Connect to QuickBooks\" to begin the secure authorization process'\n  }, {\n    label: \"QuickBooks Login\",\n    description: \"Log in to your QuickBooks Online account\"\n  }, {\n    label: \"Grant Permissions\",\n    description: \"Allow ValiSights to access your QuickBooks data\"\n  }, {\n    label: \"Complete Setup\",\n    description: \"Finalize the connection and start syncing your data\"\n  }];\n  const benefits = [{\n    icon: /*#__PURE__*/_jsxDEV(SyncIcon, {\n      sx: {\n        color: \"primary.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 13\n    }, this),\n    title: \"Real-time Data Synchronization\",\n    description: \"Automatically sync your financial data in real-time without manual uploads\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AssessmentIcon, {\n      sx: {\n        color: \"success.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 13\n    }, this),\n    title: \"Comprehensive Financial Reports\",\n    description: \"Access trial balance, profit & loss, balance sheet, and aging reports instantly\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SecurityIcon, {\n      sx: {\n        color: \"warning.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 13\n    }, this),\n    title: \"Bank-level Security\",\n    description: \"Your data is protected with OAuth 2.0 and enterprise-grade encryption\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(SpeedIcon, {\n      sx: {\n        color: \"info.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 13\n    }, this),\n    title: \"Faster Analysis\",\n    description: \"Eliminate manual data entry and get insights faster than ever before\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(AnalyticsIcon, {\n      sx: {\n        color: \"secondary.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 13\n    }, this),\n    title: \"Advanced Analytics\",\n    description: \"Unlock powerful financial analytics and benchmarking capabilities\"\n  }, {\n    icon: /*#__PURE__*/_jsxDEV(CloudSyncIcon, {\n      sx: {\n        color: \"primary.main\",\n        fontSize: 32\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 13\n    }, this),\n    title: \"Always Up-to-date\",\n    description: \"Your financial data stays current automatically across all reports\"\n  }];\n  const fetchCompanyData = useCallback(async () => {\n    try {\n      setLoading(true);\n      const response = await getOneById(companyId);\n      if (response.data.success) {\n        setCompany(response.data.company);\n        setQboConnected(response.data.company.qboConnectionStatus === \"CONNECTED\");\n        setQboStatus(response.data.company);\n      }\n    } catch (error) {\n      console.error(\"Error fetching company:\", error);\n      Swal.fire({\n        icon: \"error\",\n        title: \"Error\",\n        text: \"Failed to load company information. Please try again.\",\n        confirmButtonColor: \"#033BD7\"\n      });\n    } finally {\n      setLoading(false);\n    }\n  }, [companyId]);\n  const fetchQBOStatus = useCallback(async () => {\n    try {\n      setQboLoading(true);\n      const response = await getQBOStatus(companyId);\n      if (response.data) {\n        setQboConnected(response.data.qboConnectionStatus === \"CONNECTED\");\n        setQboStatus(response.data);\n      }\n    } catch (error) {\n      console.error(\"Error fetching QBO status:\", error);\n      setQboConnected(false);\n    } finally {\n      setQboLoading(false);\n    }\n  }, [companyId]);\n  const handleQBOConnect = async () => {\n    try {\n      var _response$data, _response$data$data;\n      setQboLoading(true);\n      setActiveStep(1);\n      const response = await connectQBO(companyId);\n      if (response.data.success && (_response$data = response.data) !== null && _response$data !== void 0 && (_response$data$data = _response$data.data) !== null && _response$data$data !== void 0 && _response$data$data.url) {\n        var _response$data2, _response$data2$data;\n        // Redirect to QuickBooks authorization\n        window.location.replace((_response$data2 = response.data) === null || _response$data2 === void 0 ? void 0 : (_response$data2$data = _response$data2.data) === null || _response$data2$data === void 0 ? void 0 : _response$data2$data.url);\n      } else {\n        throw new Error(\"Failed to get authorization URL\");\n      }\n    } catch (error) {\n      console.error(\"Error connecting to QBO:\", error);\n      setQboLoading(false);\n      setActiveStep(0);\n      Swal.fire({\n        icon: \"error\",\n        title: \"Connection Failed\",\n        text: \"Failed to initiate QuickBooks connection. Please try again.\",\n        confirmButtonColor: \"#033BD7\"\n      });\n    }\n  };\n  const handleQBODisconnect = async () => {\n    try {\n      const result = await Swal.fire({\n        title: \"Disconnect ValiSights from QuickBooks?\",\n        icon: \"warning\",\n        showCancelButton: true,\n        confirmButtonColor: \"#ef4444\",\n        // Modern red for destructive action\n        cancelButtonColor: \"#10b981\",\n        // Modern green for safe action\n        confirmButtonText: \"Yes, disconnect\",\n        cancelButtonText: \"Keep connected\",\n        width: 500\n      });\n      if (result.isConfirmed) {\n        setQboLoading(true);\n        await disconnectQBO(companyId);\n        await fetchQBOStatus();\n        Swal.fire({\n          icon: \"success\",\n          title: \"Successfully Disconnected\",\n          text: \"ValiSights has been disconnected from QuickBooks. You can reconnect anytime.\",\n          confirmButtonColor: \"#033BD7\"\n        });\n      }\n    } catch (error) {\n      console.error(\"Error disconnecting QBO:\", error);\n      Swal.fire({\n        icon: \"error\",\n        title: \"Disconnect Failed\",\n        text: \"Failed to disconnect ValiSights from QuickBooks. Please check your connection and try again.\",\n        confirmButtonColor: \"#033BD7\"\n      });\n    } finally {\n      setQboLoading(false);\n    }\n  };\n  useEffect(() => {\n    if (companyId) {\n      fetchCompanyData();\n    }\n  }, [companyId, fetchCompanyData]);\n  useEffect(() => {\n    if (qboLoading) {\n      const interval = setInterval(() => {\n        setConnectionProgress(prev => {\n          if (prev >= 90) return 90;\n          return prev + Math.random() * 10;\n        });\n      }, 200);\n      return () => clearInterval(interval);\n    } else {\n      setConnectionProgress(qboConnected ? 100 : 0);\n    }\n  }, [qboLoading, qboConnected]);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      maxWidth: \"lg\",\n      sx: {\n        py: 4\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        display: \"flex\",\n        justifyContent: \"center\",\n        alignItems: \"center\",\n        minHeight: \"400px\",\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 40\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 255,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 248,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    sx: {\n      py: 4\n    },\n    children: /*#__PURE__*/_jsxDEV(Fade, {\n      in: true,\n      timeout: 600,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          textAlign: \"center\",\n          sx: {\n            mb: 6\n          },\n          children: [/*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            spacing: 3,\n            sx: {\n              mb: 3\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              component: \"img\",\n              src: qboButton,\n              alt: \"QuickBooks\",\n              sx: {\n                height: 60,\n                width: \"auto\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 274,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h3\",\n              fontWeight: 700,\n              color: \"primary.main\",\n              children: \"+\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              fontWeight: 700,\n              children: \"ValiSights\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h4\",\n            fontWeight: 600,\n            gutterBottom: true,\n            children: [\"Connect \", company === null || company === void 0 ? void 0 : company.name, \" to QuickBooks\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 288,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            color: \"text.secondary\",\n            sx: {\n              maxWidth: 600,\n              mx: \"auto\"\n            },\n            children: \"Streamline your financial analysis with seamless QuickBooks integration\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            p: 4,\n            mb: 4,\n            border: \"2px solid\",\n            borderColor: qboConnected ? \"success.main\" : \"grey.300\",\n            borderRadius: 3,\n            background: qboConnected ? \"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)\" : \"linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)\",\n            textAlign: \"center\"\n          },\n          children: qboConnected ? /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 3,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleIcon, {\n              sx: {\n                fontSize: 64,\n                color: \"success.main\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: 600,\n              color: \"success.dark\",\n              children: \"Successfully Connected!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Alert, {\n              severity: \"success\",\n              sx: {\n                maxWidth: 500\n              },\n              children: [/*#__PURE__*/_jsxDEV(AlertTitle, {\n                children: [\"Connected to \", (company === null || company === void 0 ? void 0 : company.qboCompanyName) || \"QuickBooks\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 19\n              }, this), \"Your financial data is now synchronized and ready for analysis.\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Stack, {\n              direction: \"row\",\n              spacing: 2,\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: \"outlined\",\n                startIcon: /*#__PURE__*/_jsxDEV(RefreshIcon, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 333,\n                  columnNumber: 32\n                }, this),\n                onClick: fetchQBOStatus,\n                disabled: qboLoading,\n                children: \"Refresh Status\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 331,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                color: \"error\",\n                onClick: handleQBODisconnect,\n                disabled: qboLoading,\n                children: \"Disconnect\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: \"contained\",\n                onClick: () => navigate(`/companies/${companyId}`),\n                children: \"View Company Dashboard\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(Stack, {\n            spacing: 3,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(ErrorIcon, {\n              sx: {\n                fontSize: 64,\n                color: \"error.main\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h5\",\n              fontWeight: 600,\n              children: \"Ready to Connect\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this), qboLoading && /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: \"100%\",\n                maxWidth: 400\n              },\n              children: [/*#__PURE__*/_jsxDEV(LinearProgress, {\n                variant: \"determinate\",\n                value: connectionProgress,\n                sx: {\n                  height: 8,\n                  borderRadius: 4,\n                  mb: 2\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"Establishing secure connection...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 369,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 363,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                position: \"relative\",\n                display: \"inline-block\"\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: qboButton,\n                alt: \"Connect to QuickBooks\",\n                sx: {\n                  cursor: \"pointer\",\n                  height: 60,\n                  width: \"auto\",\n                  transition: \"opacity 0.2s ease\",\n                  \"&:hover\": {\n                    opacity: 0\n                  },\n                  opacity: qboLoading ? 0.5 : 1,\n                  pointerEvents: qboLoading ? \"none\" : \"auto\"\n                },\n                onClick: !qboLoading ? handleQBOConnect : undefined\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                component: \"img\",\n                src: qboButtonHover,\n                alt: \"Connect to QuickBooks\",\n                sx: {\n                  position: \"absolute\",\n                  top: 0,\n                  left: 0,\n                  height: 60,\n                  width: \"auto\",\n                  opacity: 0,\n                  transition: \"opacity 0.2s ease\",\n                  \"&:hover\": {\n                    opacity: 1\n                  },\n                  pointerEvents: qboLoading ? \"none\" : \"auto\"\n                },\n                onClick: !qboLoading ? handleQBOConnect : undefined\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 375,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 356,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 11\n        }, this), !qboConnected && /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            p: 4,\n            mb: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h5\",\n            fontWeight: 600,\n            gutterBottom: true,\n            children: \"How It Works\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Stepper, {\n            activeStep: activeStep,\n            orientation: \"vertical\",\n            children: connectionSteps.map((step, index) => /*#__PURE__*/_jsxDEV(Step, {\n              children: [/*#__PURE__*/_jsxDEV(StepLabel, {\n                children: step.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 422,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(StepContent, {\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: step.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 424,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 423,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 421,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h5\",\n          fontWeight: 600,\n          textAlign: \"center\",\n          sx: {\n            mb: 4\n          },\n          children: \"Why Connect to QuickBooks?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: benefits.map((benefit, index) => /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            md: 6,\n            lg: 4,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              sx: {\n                p: 3,\n                height: \"100%\",\n                transition: \"transform 0.2s ease, box-shadow 0.2s ease\",\n                \"&:hover\": {\n                  transform: \"translateY(-4px)\",\n                  boxShadow: \"0 8px 25px rgba(0,0,0,0.15)\"\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Stack, {\n                spacing: 2,\n                alignItems: \"center\",\n                textAlign: \"center\",\n                children: [benefit.icon, /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  fontWeight: 600,\n                  children: benefit.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 460,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: benefit.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 458,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 444,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          sx: {\n            p: 3,\n            mt: 4,\n            backgroundColor: \"grey.50\"\n          },\n          children: /*#__PURE__*/_jsxDEV(Stack, {\n            direction: \"row\",\n            spacing: 2,\n            alignItems: \"center\",\n            children: [/*#__PURE__*/_jsxDEV(ShieldIcon, {\n              sx: {\n                color: \"success.main\",\n                fontSize: 32\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 475,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"h6\",\n                fontWeight: 600,\n                gutterBottom: true,\n                children: \"Your Data is Secure\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 477,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                color: \"text.secondary\",\n                children: \"We use OAuth 2.0 authentication and bank-level encryption to protect your financial data. ValiSights never stores your QuickBooks login credentials.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 476,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickBooksConnect, \"dTnhM13QrdpP6MpJM7PVZ/re6Bo=\", false, function () {\n  return [useParams, useNavigate];\n});\n_c = QuickBooksConnect;\nexport default QuickBooksConnect;\nvar _c;\n$RefreshReg$(_c, \"QuickBooksConnect\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "Box", "Container", "Card", "Typography", "<PERSON><PERSON>", "<PERSON><PERSON>", "Chip", "CircularProgress", "Fade", "Paper", "Divider", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LinearProgress", "Grid", "List", "ListItem", "ListItemIcon", "ListItemText", "Stepper", "Step", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "CheckCircleIcon", "Error", "ErrorIcon", "Refresh", "RefreshIcon", "AccountBalance", "AccountBalanceIcon", "Security", "SecurityIcon", "Sync", "SyncIcon", "TrendingUp", "TrendingUpIcon", "Assessment", "AssessmentIcon", "CloudSync", "CloudSyncIcon", "Speed", "SpeedIcon", "Shield", "ShieldIcon", "Analytics", "AnalyticsIcon", "useParams", "useNavigate", "<PERSON><PERSON>", "getOneById", "connectQBO", "disconnectQBO", "getQBOStatus", "qboButton", "qboButtonHover", "jsxDEV", "_jsxDEV", "QuickBooksConnect", "_s", "companyId", "navigate", "company", "setCompany", "qboConnected", "setQboConnected", "qboLoading", "setQboLoading", "qboStatus", "setQboStatus", "loading", "setLoading", "connectionProgress", "setConnectionProgress", "activeStep", "setActiveStep", "connectionSteps", "label", "description", "benefits", "icon", "sx", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "title", "fetchCompanyData", "response", "data", "success", "qboConnectionStatus", "error", "console", "fire", "text", "confirmButtonColor", "fetchQBOStatus", "handleQBOConnect", "_response$data", "_response$data$data", "url", "_response$data2", "_response$data2$data", "window", "location", "replace", "handleQBODisconnect", "result", "showCancelButton", "cancelButtonColor", "confirmButtonText", "cancelButtonText", "width", "isConfirmed", "interval", "setInterval", "prev", "Math", "random", "clearInterval", "max<PERSON><PERSON><PERSON>", "py", "children", "display", "justifyContent", "alignItems", "minHeight", "size", "in", "timeout", "textAlign", "mb", "direction", "spacing", "component", "src", "alt", "height", "variant", "fontWeight", "gutterBottom", "name", "mx", "p", "border", "borderColor", "borderRadius", "background", "severity", "qboCompanyName", "startIcon", "onClick", "disabled", "value", "position", "cursor", "transition", "opacity", "pointerEvents", "undefined", "top", "left", "orientation", "map", "step", "index", "container", "benefit", "item", "xs", "md", "lg", "transform", "boxShadow", "mt", "backgroundColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/pages/QuickBooksConnect/QuickBooksConnect.jsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from \"react\";\r\nimport {\r\n  Box,\r\n  Container,\r\n  Card,\r\n  Typography,\r\n  Button,\r\n  Stack,\r\n  Chip,\r\n  CircularProgress,\r\n  Fade,\r\n  Paper,\r\n  Divider,\r\n  Alert,\r\n  AlertTitle,\r\n  LinearProgress,\r\n  Grid,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Stepper,\r\n  Step,\r\n  StepLabel,\r\n  StepContent,\r\n} from \"@mui/material\";\r\nimport {\r\n  CheckCircle as CheckCircleIcon,\r\n  Error as ErrorIcon,\r\n  Refresh as RefreshIcon,\r\n  AccountBalance as AccountBalanceIcon,\r\n  Security as SecurityIcon,\r\n  Sync as SyncIcon,\r\n  TrendingUp as TrendingUpIcon,\r\n  Assessment as AssessmentIcon,\r\n  CloudSync as CloudSyncIcon,\r\n  Speed as SpeedIcon,\r\n  Shield as ShieldIcon,\r\n  Analytics as AnalyticsIcon,\r\n} from \"@mui/icons-material\";\r\nimport { useParams, useNavigate } from \"react-router-dom\";\r\nimport Swal from \"sweetalert2\";\r\nimport { getOneById } from \"../../services/company\";\r\nimport { connectQBO, disconnectQBO, getQBOStatus } from \"../../services/qbo\";\r\nimport qboButton from \"../../assets/C2QB_green_btn_med_default.svg\";\r\nimport qboButtonHover from \"../../assets/C2QB_green_btn_med_hover.svg\";\r\n \r\nconst QuickBooksConnect = () => {\r\n  const { companyId } = useParams();\r\n  const navigate = useNavigate();\r\n\r\n  const [company, setCompany] = useState(null);\r\n  const [qboConnected, setQboConnected] = useState(false);\r\n  const [qboLoading, setQboLoading] = useState(false);\r\n  const [qboStatus, setQboStatus] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [connectionProgress, setConnectionProgress] = useState(0);\r\n  const [activeStep, setActiveStep] = useState(0);\r\n\r\n  const connectionSteps = [\r\n    {\r\n      label: \"Authorize Connection\",\r\n      description:\r\n        'Click \"Connect to QuickBooks\" to begin the secure authorization process',\r\n    },\r\n    {\r\n      label: \"QuickBooks Login\",\r\n      description: \"Log in to your QuickBooks Online account\",\r\n    },\r\n    {\r\n      label: \"Grant Permissions\",\r\n      description: \"Allow ValiSights to access your QuickBooks data\",\r\n    },\r\n    {\r\n      label: \"Complete Setup\",\r\n      description: \"Finalize the connection and start syncing your data\",\r\n    },\r\n  ];\r\n\r\n  const benefits = [\r\n    {\r\n      icon: <SyncIcon sx={{ color: \"primary.main\", fontSize: 32 }} />,\r\n      title: \"Real-time Data Synchronization\",\r\n      description:\r\n        \"Automatically sync your financial data in real-time without manual uploads\",\r\n    },\r\n    {\r\n      icon: <AssessmentIcon sx={{ color: \"success.main\", fontSize: 32 }} />,\r\n      title: \"Comprehensive Financial Reports\",\r\n      description:\r\n        \"Access trial balance, profit & loss, balance sheet, and aging reports instantly\",\r\n    },\r\n    {\r\n      icon: <SecurityIcon sx={{ color: \"warning.main\", fontSize: 32 }} />,\r\n      title: \"Bank-level Security\",\r\n      description:\r\n        \"Your data is protected with OAuth 2.0 and enterprise-grade encryption\",\r\n    },\r\n    {\r\n      icon: <SpeedIcon sx={{ color: \"info.main\", fontSize: 32 }} />,\r\n      title: \"Faster Analysis\",\r\n      description:\r\n        \"Eliminate manual data entry and get insights faster than ever before\",\r\n    },\r\n    {\r\n      icon: <AnalyticsIcon sx={{ color: \"secondary.main\", fontSize: 32 }} />,\r\n      title: \"Advanced Analytics\",\r\n      description:\r\n        \"Unlock powerful financial analytics and benchmarking capabilities\",\r\n    },\r\n    {\r\n      icon: <CloudSyncIcon sx={{ color: \"primary.main\", fontSize: 32 }} />,\r\n      title: \"Always Up-to-date\",\r\n      description:\r\n        \"Your financial data stays current automatically across all reports\",\r\n    },\r\n  ];\r\n\r\n  const fetchCompanyData = useCallback(async () => {\r\n    try {\r\n      setLoading(true);\r\n      const response = await getOneById(companyId);\r\n\r\n      if (response.data.success) {\r\n        setCompany(response.data.company);\r\n        setQboConnected(\r\n          response.data.company.qboConnectionStatus === \"CONNECTED\"\r\n        );\r\n        setQboStatus(response.data.company);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching company:\", error);\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Error\",\r\n        text: \"Failed to load company information. Please try again.\",\r\n        confirmButtonColor: \"#033BD7\",\r\n      });\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [companyId]);\r\n\r\n  const fetchQBOStatus = useCallback(async () => {\r\n    try {\r\n      setQboLoading(true);\r\n      const response = await getQBOStatus(companyId);\r\n      if (response.data) {\r\n        setQboConnected(response.data.qboConnectionStatus === \"CONNECTED\");\r\n        setQboStatus(response.data);\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error fetching QBO status:\", error);\r\n      setQboConnected(false);\r\n    } finally {\r\n      setQboLoading(false);\r\n    }\r\n  }, [companyId]);\r\n\r\n  const handleQBOConnect = async () => {\r\n    try {\r\n      setQboLoading(true);\r\n      setActiveStep(1);\r\n\r\n      const response = await connectQBO(companyId);\r\n\r\n      if (response.data.success && response.data?.data?.url) {\r\n        // Redirect to QuickBooks authorization\r\n        window.location.replace(response.data?.data?.url);\r\n      } else {\r\n        throw new Error(\"Failed to get authorization URL\");\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error connecting to QBO:\", error);\r\n      setQboLoading(false);\r\n      setActiveStep(0);\r\n\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Connection Failed\",\r\n        text: \"Failed to initiate QuickBooks connection. Please try again.\",\r\n        confirmButtonColor: \"#033BD7\",\r\n      });\r\n    }\r\n  };\r\n\r\n  const handleQBODisconnect = async () => {\r\n    try {\r\n      const result = await Swal.fire({\r\n        title: \"Disconnect ValiSights from QuickBooks?\",\r\n\r\n        icon: \"warning\",\r\n        showCancelButton: true,\r\n        confirmButtonColor: \"#ef4444\", // Modern red for destructive action\r\n        cancelButtonColor: \"#10b981\", // Modern green for safe action\r\n        confirmButtonText: \"Yes, disconnect\",\r\n        cancelButtonText: \"Keep connected\",\r\n        width: 500,\r\n      });\r\n\r\n      if (result.isConfirmed) {\r\n        setQboLoading(true);\r\n        await disconnectQBO(companyId);\r\n        await fetchQBOStatus();\r\n\r\n        Swal.fire({\r\n          icon: \"success\",\r\n          title: \"Successfully Disconnected\",\r\n          text: \"ValiSights has been disconnected from QuickBooks. You can reconnect anytime.\",\r\n          confirmButtonColor: \"#033BD7\",\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error disconnecting QBO:\", error);\r\n      Swal.fire({\r\n        icon: \"error\",\r\n        title: \"Disconnect Failed\",\r\n        text: \"Failed to disconnect ValiSights from QuickBooks. Please check your connection and try again.\",\r\n        confirmButtonColor: \"#033BD7\",\r\n      });\r\n    } finally {\r\n      setQboLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (companyId) {\r\n      fetchCompanyData();\r\n    }\r\n  }, [companyId, fetchCompanyData]);\r\n\r\n  useEffect(() => {\r\n    if (qboLoading) {\r\n      const interval = setInterval(() => {\r\n        setConnectionProgress((prev) => {\r\n          if (prev >= 90) return 90;\r\n          return prev + Math.random() * 10;\r\n        });\r\n      }, 200);\r\n      return () => clearInterval(interval);\r\n    } else {\r\n      setConnectionProgress(qboConnected ? 100 : 0);\r\n    }\r\n  }, [qboLoading, qboConnected]);\r\n\r\n  if (loading) {\r\n    return (\r\n      <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n        <Box\r\n          display=\"flex\"\r\n          justifyContent=\"center\"\r\n          alignItems=\"center\"\r\n          minHeight=\"400px\"\r\n        >\r\n          <CircularProgress size={40} />\r\n        </Box>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container maxWidth=\"lg\" sx={{ py: 4 }}>\r\n      <Fade in timeout={600}>\r\n        <Box>\r\n          {/* Header */}\r\n          <Box textAlign=\"center\" sx={{ mb: 6 }}>\r\n            <Stack\r\n              direction=\"row\"\r\n              justifyContent=\"center\"\r\n              alignItems=\"center\"\r\n              spacing={3}\r\n              sx={{ mb: 3 }}\r\n            >\r\n              <Box\r\n                component=\"img\"\r\n                src={qboButton}\r\n                alt=\"QuickBooks\"\r\n                sx={{ height: 60, width: \"auto\" }}\r\n              />\r\n              <Typography variant=\"h3\" fontWeight={700} color=\"primary.main\">\r\n                +\r\n              </Typography>\r\n              <Typography variant=\"h4\" fontWeight={700}>\r\n                ValiSights\r\n              </Typography>\r\n            </Stack>\r\n\r\n            <Typography variant=\"h4\" fontWeight={600} gutterBottom>\r\n              Connect {company?.name} to QuickBooks\r\n            </Typography>\r\n\r\n            <Typography\r\n              variant=\"h6\"\r\n              color=\"text.secondary\"\r\n              sx={{ maxWidth: 600, mx: \"auto\" }}\r\n            >\r\n              Streamline your financial analysis with seamless QuickBooks\r\n              integration\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Connection Status Card */}\r\n          <Card\r\n            sx={{\r\n              p: 4,\r\n              mb: 4,\r\n              border: \"2px solid\",\r\n              borderColor: qboConnected ? \"success.main\" : \"grey.300\",\r\n              borderRadius: 3,\r\n              background: qboConnected\r\n                ? \"linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)\"\r\n                : \"linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%)\",\r\n              textAlign: \"center\",\r\n            }}\r\n          >\r\n            {qboConnected ? (\r\n              <Stack spacing={3} alignItems=\"center\">\r\n                <CheckCircleIcon sx={{ fontSize: 64, color: \"success.main\" }} />\r\n                <Typography variant=\"h5\" fontWeight={600} color=\"success.dark\">\r\n                  Successfully Connected!\r\n                </Typography>\r\n                <Alert severity=\"success\" sx={{ maxWidth: 500 }}>\r\n                  <AlertTitle>\r\n                    Connected to {company?.qboCompanyName || \"QuickBooks\"}\r\n                  </AlertTitle>\r\n                  Your financial data is now synchronized and ready for\r\n                  analysis.\r\n                </Alert>\r\n\r\n                <Stack direction=\"row\" spacing={2}>\r\n                  <Button\r\n                    variant=\"outlined\"\r\n                    startIcon={<RefreshIcon />}\r\n                    onClick={fetchQBOStatus}\r\n                    disabled={qboLoading}\r\n                  >\r\n                    Refresh Status\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    color=\"error\"\r\n                    onClick={handleQBODisconnect}\r\n                    disabled={qboLoading}\r\n                  >\r\n                    Disconnect\r\n                  </Button>\r\n                  <Button\r\n                    variant=\"contained\"\r\n                    onClick={() => navigate(`/companies/${companyId}`)}\r\n                  >\r\n                    View Company Dashboard\r\n                  </Button>\r\n                </Stack>\r\n              </Stack>\r\n            ) : (\r\n              <Stack spacing={3} alignItems=\"center\">\r\n                <ErrorIcon sx={{ fontSize: 64, color: \"error.main\" }} />\r\n                <Typography variant=\"h5\" fontWeight={600}>\r\n                  Ready to Connect\r\n                </Typography>\r\n\r\n                {qboLoading && (\r\n                  <Box sx={{ width: \"100%\", maxWidth: 400 }}>\r\n                    <LinearProgress\r\n                      variant=\"determinate\"\r\n                      value={connectionProgress}\r\n                      sx={{ height: 8, borderRadius: 4, mb: 2 }}\r\n                    />\r\n                    <Typography variant=\"body2\" color=\"text.secondary\">\r\n                      Establishing secure connection...\r\n                    </Typography>\r\n                  </Box>\r\n                )}\r\n\r\n                <Box sx={{ position: \"relative\", display: \"inline-block\" }}>\r\n                  <Box\r\n                    component=\"img\"\r\n                    src={qboButton}\r\n                    alt=\"Connect to QuickBooks\"\r\n                    sx={{\r\n                      cursor: \"pointer\",\r\n                      height: 60,\r\n                      width: \"auto\",\r\n                      transition: \"opacity 0.2s ease\",\r\n                      \"&:hover\": { opacity: 0 },\r\n                      opacity: qboLoading ? 0.5 : 1,\r\n                      pointerEvents: qboLoading ? \"none\" : \"auto\",\r\n                    }}\r\n                    onClick={!qboLoading ? handleQBOConnect : undefined}\r\n                  />\r\n                  <Box\r\n                    component=\"img\"\r\n                    src={qboButtonHover}\r\n                    alt=\"Connect to QuickBooks\"\r\n                    sx={{\r\n                      position: \"absolute\",\r\n                      top: 0,\r\n                      left: 0,\r\n                      height: 60,\r\n                      width: \"auto\",\r\n                      opacity: 0,\r\n                      transition: \"opacity 0.2s ease\",\r\n                      \"&:hover\": { opacity: 1 },\r\n                      pointerEvents: qboLoading ? \"none\" : \"auto\",\r\n                    }}\r\n                    onClick={!qboLoading ? handleQBOConnect : undefined}\r\n                  />\r\n                </Box>\r\n              </Stack>\r\n            )}\r\n          </Card>\r\n\r\n          {/* Connection Steps */}\r\n          {!qboConnected && (\r\n            <Card sx={{ p: 4, mb: 4 }}>\r\n              <Typography variant=\"h5\" fontWeight={600} gutterBottom>\r\n                How It Works\r\n              </Typography>\r\n              <Stepper activeStep={activeStep} orientation=\"vertical\">\r\n                {connectionSteps.map((step, index) => (\r\n                  <Step key={index}>\r\n                    <StepLabel>{step.label}</StepLabel>\r\n                    <StepContent>\r\n                      <Typography variant=\"body2\" color=\"text.secondary\">\r\n                        {step.description}\r\n                      </Typography>\r\n                    </StepContent>\r\n                  </Step>\r\n                ))}\r\n              </Stepper>\r\n            </Card>\r\n          )}\r\n\r\n          {/* Benefits Grid */}\r\n          <Typography\r\n            variant=\"h5\"\r\n            fontWeight={600}\r\n            textAlign=\"center\"\r\n            sx={{ mb: 4 }}\r\n          >\r\n            Why Connect to QuickBooks?\r\n          </Typography>\r\n\r\n          <Grid container spacing={3}>\r\n            {benefits.map((benefit, index) => (\r\n              <Grid item xs={12} md={6} lg={4} key={index}>\r\n                <Card\r\n                  sx={{\r\n                    p: 3,\r\n                    height: \"100%\",\r\n                    transition: \"transform 0.2s ease, box-shadow 0.2s ease\",\r\n                    \"&:hover\": {\r\n                      transform: \"translateY(-4px)\",\r\n                      boxShadow: \"0 8px 25px rgba(0,0,0,0.15)\",\r\n                    },\r\n                  }}\r\n                >\r\n                  <Stack spacing={2} alignItems=\"center\" textAlign=\"center\">\r\n                    {benefit.icon}\r\n                    <Typography variant=\"h6\" fontWeight={600}>\r\n                      {benefit.title}\r\n                    </Typography>\r\n                    <Typography variant=\"body2\" color=\"text.secondary\">\r\n                      {benefit.description}\r\n                    </Typography>\r\n                  </Stack>\r\n                </Card>\r\n              </Grid>\r\n            ))}\r\n          </Grid>\r\n\r\n          {/* Security Notice */}\r\n          <Card sx={{ p: 3, mt: 4, backgroundColor: \"grey.50\" }}>\r\n            <Stack direction=\"row\" spacing={2} alignItems=\"center\">\r\n              <ShieldIcon sx={{ color: \"success.main\", fontSize: 32 }} />\r\n              <Box>\r\n                <Typography variant=\"h6\" fontWeight={600} gutterBottom>\r\n                  Your Data is Secure\r\n                </Typography>\r\n                <Typography variant=\"body2\" color=\"text.secondary\">\r\n                  We use OAuth 2.0 authentication and bank-level encryption to\r\n                  protect your financial data. ValiSights never stores your\r\n                  QuickBooks login credentials.\r\n                </Typography>\r\n              </Box>\r\n            </Stack>\r\n          </Card>\r\n        </Box>\r\n      </Fade>\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default QuickBooksConnect;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SACEC,GAAG,EACHC,SAAS,EACTC,IAAI,EACJC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,gBAAgB,EAChBC,IAAI,EACJC,KAAK,EACLC,OAAO,EACPC,KAAK,EACLC,UAAU,EACVC,cAAc,EACdC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,OAAO,EACPC,IAAI,EACJC,SAAS,EACTC,WAAW,QACN,eAAe;AACtB,SACEC,WAAW,IAAIC,eAAe,EAC9BC,KAAK,IAAIC,SAAS,EAClBC,OAAO,IAAIC,WAAW,EACtBC,cAAc,IAAIC,kBAAkB,EACpCC,QAAQ,IAAIC,YAAY,EACxBC,IAAI,IAAIC,QAAQ,EAChBC,UAAU,IAAIC,cAAc,EAC5BC,UAAU,IAAIC,cAAc,EAC5BC,SAAS,IAAIC,aAAa,EAC1BC,KAAK,IAAIC,SAAS,EAClBC,MAAM,IAAIC,UAAU,EACpBC,SAAS,IAAIC,aAAa,QACrB,qBAAqB;AAC5B,SAASC,SAAS,EAAEC,WAAW,QAAQ,kBAAkB;AACzD,OAAOC,IAAI,MAAM,aAAa;AAC9B,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,UAAU,EAAEC,aAAa,EAAEC,YAAY,QAAQ,oBAAoB;AAC5E,OAAOC,SAAS,MAAM,6CAA6C;AACnE,OAAOC,cAAc,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvE,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM;IAAEC;EAAU,CAAC,GAAGb,SAAS,CAAC,CAAC;EACjC,MAAMc,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAE9B,MAAM,CAACc,OAAO,EAAEC,UAAU,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmE,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,UAAU,EAAEC,aAAa,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuE,SAAS,EAAEC,YAAY,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACyE,OAAO,EAAEC,UAAU,CAAC,GAAG1E,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2E,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5E,QAAQ,CAAC,CAAC,CAAC;EAC/D,MAAM,CAAC6E,UAAU,EAAEC,aAAa,CAAC,GAAG9E,QAAQ,CAAC,CAAC,CAAC;EAE/C,MAAM+E,eAAe,GAAG,CACtB;IACEC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE;EACf,CAAC,EACD;IACED,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,eAAEvB,OAAA,CAACvB,QAAQ;MAAC+C,EAAE,EAAE;QAAEC,KAAK,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC/DC,KAAK,EAAE,gCAAgC;IACvCV,WAAW,EACT;EACJ,CAAC,EACD;IACEE,IAAI,eAAEvB,OAAA,CAACnB,cAAc;MAAC2C,EAAE,EAAE;QAAEC,KAAK,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACrEC,KAAK,EAAE,iCAAiC;IACxCV,WAAW,EACT;EACJ,CAAC,EACD;IACEE,IAAI,eAAEvB,OAAA,CAACzB,YAAY;MAACiD,EAAE,EAAE;QAAEC,KAAK,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACnEC,KAAK,EAAE,qBAAqB;IAC5BV,WAAW,EACT;EACJ,CAAC,EACD;IACEE,IAAI,eAAEvB,OAAA,CAACf,SAAS;MAACuC,EAAE,EAAE;QAAEC,KAAK,EAAE,WAAW;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC7DC,KAAK,EAAE,iBAAiB;IACxBV,WAAW,EACT;EACJ,CAAC,EACD;IACEE,IAAI,eAAEvB,OAAA,CAACX,aAAa;MAACmC,EAAE,EAAE;QAAEC,KAAK,EAAE,gBAAgB;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtEC,KAAK,EAAE,oBAAoB;IAC3BV,WAAW,EACT;EACJ,CAAC,EACD;IACEE,IAAI,eAAEvB,OAAA,CAACjB,aAAa;MAACyC,EAAE,EAAE;QAAEC,KAAK,EAAE,cAAc;QAAEC,QAAQ,EAAE;MAAG;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACpEC,KAAK,EAAE,mBAAmB;IAC1BV,WAAW,EACT;EACJ,CAAC,CACF;EAED,MAAMW,gBAAgB,GAAG1F,WAAW,CAAC,YAAY;IAC/C,IAAI;MACFwE,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMmB,QAAQ,GAAG,MAAMxC,UAAU,CAACU,SAAS,CAAC;MAE5C,IAAI8B,QAAQ,CAACC,IAAI,CAACC,OAAO,EAAE;QACzB7B,UAAU,CAAC2B,QAAQ,CAACC,IAAI,CAAC7B,OAAO,CAAC;QACjCG,eAAe,CACbyB,QAAQ,CAACC,IAAI,CAAC7B,OAAO,CAAC+B,mBAAmB,KAAK,WAChD,CAAC;QACDxB,YAAY,CAACqB,QAAQ,CAACC,IAAI,CAAC7B,OAAO,CAAC;MACrC;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7C,IAAI,CAAC+C,IAAI,CAAC;QACRhB,IAAI,EAAE,OAAO;QACbQ,KAAK,EAAE,OAAO;QACdS,IAAI,EAAE,uDAAuD;QAC7DC,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACX,SAAS,CAAC,CAAC;EAEf,MAAMuC,cAAc,GAAGpG,WAAW,CAAC,YAAY;IAC7C,IAAI;MACFoE,aAAa,CAAC,IAAI,CAAC;MACnB,MAAMuB,QAAQ,GAAG,MAAMrC,YAAY,CAACO,SAAS,CAAC;MAC9C,IAAI8B,QAAQ,CAACC,IAAI,EAAE;QACjB1B,eAAe,CAACyB,QAAQ,CAACC,IAAI,CAACE,mBAAmB,KAAK,WAAW,CAAC;QAClExB,YAAY,CAACqB,QAAQ,CAACC,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD7B,eAAe,CAAC,KAAK,CAAC;IACxB,CAAC,SAAS;MACRE,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,EAAE,CAACP,SAAS,CAAC,CAAC;EAEf,MAAMwC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MAAA,IAAAC,cAAA,EAAAC,mBAAA;MACFnC,aAAa,CAAC,IAAI,CAAC;MACnBQ,aAAa,CAAC,CAAC,CAAC;MAEhB,MAAMe,QAAQ,GAAG,MAAMvC,UAAU,CAACS,SAAS,CAAC;MAE5C,IAAI8B,QAAQ,CAACC,IAAI,CAACC,OAAO,KAAAS,cAAA,GAAIX,QAAQ,CAACC,IAAI,cAAAU,cAAA,gBAAAC,mBAAA,GAAbD,cAAA,CAAeV,IAAI,cAAAW,mBAAA,eAAnBA,mBAAA,CAAqBC,GAAG,EAAE;QAAA,IAAAC,eAAA,EAAAC,oBAAA;QACrD;QACAC,MAAM,CAACC,QAAQ,CAACC,OAAO,EAAAJ,eAAA,GAACd,QAAQ,CAACC,IAAI,cAAAa,eAAA,wBAAAC,oBAAA,GAAbD,eAAA,CAAeb,IAAI,cAAAc,oBAAA,uBAAnBA,oBAAA,CAAqBF,GAAG,CAAC;MACnD,CAAC,MAAM;QACL,MAAM,IAAI9E,KAAK,CAAC,iCAAiC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOqE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD3B,aAAa,CAAC,KAAK,CAAC;MACpBQ,aAAa,CAAC,CAAC,CAAC;MAEhB1B,IAAI,CAAC+C,IAAI,CAAC;QACRhB,IAAI,EAAE,OAAO;QACbQ,KAAK,EAAE,mBAAmB;QAC1BS,IAAI,EAAE,6DAA6D;QACnEC,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMW,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAM7D,IAAI,CAAC+C,IAAI,CAAC;QAC7BR,KAAK,EAAE,wCAAwC;QAE/CR,IAAI,EAAE,SAAS;QACf+B,gBAAgB,EAAE,IAAI;QACtBb,kBAAkB,EAAE,SAAS;QAAE;QAC/Bc,iBAAiB,EAAE,SAAS;QAAE;QAC9BC,iBAAiB,EAAE,iBAAiB;QACpCC,gBAAgB,EAAE,gBAAgB;QAClCC,KAAK,EAAE;MACT,CAAC,CAAC;MAEF,IAAIL,MAAM,CAACM,WAAW,EAAE;QACtBjD,aAAa,CAAC,IAAI,CAAC;QACnB,MAAMf,aAAa,CAACQ,SAAS,CAAC;QAC9B,MAAMuC,cAAc,CAAC,CAAC;QAEtBlD,IAAI,CAAC+C,IAAI,CAAC;UACRhB,IAAI,EAAE,SAAS;UACfQ,KAAK,EAAE,2BAA2B;UAClCS,IAAI,EAAE,8EAA8E;UACpFC,kBAAkB,EAAE;QACtB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD7C,IAAI,CAAC+C,IAAI,CAAC;QACRhB,IAAI,EAAE,OAAO;QACbQ,KAAK,EAAE,mBAAmB;QAC1BS,IAAI,EAAE,8FAA8F;QACpGC,kBAAkB,EAAE;MACtB,CAAC,CAAC;IACJ,CAAC,SAAS;MACR/B,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAEDrE,SAAS,CAAC,MAAM;IACd,IAAI8D,SAAS,EAAE;MACb6B,gBAAgB,CAAC,CAAC;IACpB;EACF,CAAC,EAAE,CAAC7B,SAAS,EAAE6B,gBAAgB,CAAC,CAAC;EAEjC3F,SAAS,CAAC,MAAM;IACd,IAAIoE,UAAU,EAAE;MACd,MAAMmD,QAAQ,GAAGC,WAAW,CAAC,MAAM;QACjC7C,qBAAqB,CAAE8C,IAAI,IAAK;UAC9B,IAAIA,IAAI,IAAI,EAAE,EAAE,OAAO,EAAE;UACzB,OAAOA,IAAI,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QAClC,CAAC,CAAC;MACJ,CAAC,EAAE,GAAG,CAAC;MACP,OAAO,MAAMC,aAAa,CAACL,QAAQ,CAAC;IACtC,CAAC,MAAM;MACL5C,qBAAqB,CAACT,YAAY,GAAG,GAAG,GAAG,CAAC,CAAC;IAC/C;EACF,CAAC,EAAE,CAACE,UAAU,EAAEF,YAAY,CAAC,CAAC;EAE9B,IAAIM,OAAO,EAAE;IACX,oBACEb,OAAA,CAACxD,SAAS;MAAC0H,QAAQ,EAAC,IAAI;MAAC1C,EAAE,EAAE;QAAE2C,EAAE,EAAE;MAAE,CAAE;MAAAC,QAAA,eACrCpE,OAAA,CAACzD,GAAG;QACF8H,OAAO,EAAC,MAAM;QACdC,cAAc,EAAC,QAAQ;QACvBC,UAAU,EAAC,QAAQ;QACnBC,SAAS,EAAC,OAAO;QAAAJ,QAAA,eAEjBpE,OAAA,CAAClD,gBAAgB;UAAC2H,IAAI,EAAE;QAAG;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC;EAEhB;EAEA,oBACE9B,OAAA,CAACxD,SAAS;IAAC0H,QAAQ,EAAC,IAAI;IAAC1C,EAAE,EAAE;MAAE2C,EAAE,EAAE;IAAE,CAAE;IAAAC,QAAA,eACrCpE,OAAA,CAACjD,IAAI;MAAC2H,EAAE;MAACC,OAAO,EAAE,GAAI;MAAAP,QAAA,eACpBpE,OAAA,CAACzD,GAAG;QAAA6H,QAAA,gBAEFpE,OAAA,CAACzD,GAAG;UAACqI,SAAS,EAAC,QAAQ;UAACpD,EAAE,EAAE;YAAEqD,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACpCpE,OAAA,CAACpD,KAAK;YACJkI,SAAS,EAAC,KAAK;YACfR,cAAc,EAAC,QAAQ;YACvBC,UAAU,EAAC,QAAQ;YACnBQ,OAAO,EAAE,CAAE;YACXvD,EAAE,EAAE;cAAEqD,EAAE,EAAE;YAAE,CAAE;YAAAT,QAAA,gBAEdpE,OAAA,CAACzD,GAAG;cACFyI,SAAS,EAAC,KAAK;cACfC,GAAG,EAAEpF,SAAU;cACfqF,GAAG,EAAC,YAAY;cAChB1D,EAAE,EAAE;gBAAE2D,MAAM,EAAE,EAAE;gBAAEzB,KAAK,EAAE;cAAO;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnC,CAAC,eACF9B,OAAA,CAACtD,UAAU;cAAC0I,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAC5D,KAAK,EAAC,cAAc;cAAA2C,QAAA,EAAC;YAE/D;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9B,OAAA,CAACtD,UAAU;cAAC0I,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAAjB,QAAA,EAAC;YAE1C;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAER9B,OAAA,CAACtD,UAAU;YAAC0I,OAAO,EAAC,IAAI;YAACC,UAAU,EAAE,GAAI;YAACC,YAAY;YAAAlB,QAAA,GAAC,UAC7C,EAAC/D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEkF,IAAI,EAAC,gBACzB;UAAA;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eAEb9B,OAAA,CAACtD,UAAU;YACT0I,OAAO,EAAC,IAAI;YACZ3D,KAAK,EAAC,gBAAgB;YACtBD,EAAE,EAAE;cAAE0C,QAAQ,EAAE,GAAG;cAAEsB,EAAE,EAAE;YAAO,CAAE;YAAApB,QAAA,EACnC;UAGD;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGN9B,OAAA,CAACvD,IAAI;UACH+E,EAAE,EAAE;YACFiE,CAAC,EAAE,CAAC;YACJZ,EAAE,EAAE,CAAC;YACLa,MAAM,EAAE,WAAW;YACnBC,WAAW,EAAEpF,YAAY,GAAG,cAAc,GAAG,UAAU;YACvDqF,YAAY,EAAE,CAAC;YACfC,UAAU,EAAEtF,YAAY,GACpB,mDAAmD,GACnD,mDAAmD;YACvDqE,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAED7D,YAAY,gBACXP,OAAA,CAACpD,KAAK;YAACmI,OAAO,EAAE,CAAE;YAACR,UAAU,EAAC,QAAQ;YAAAH,QAAA,gBACpCpE,OAAA,CAACjC,eAAe;cAACyD,EAAE,EAAE;gBAAEE,QAAQ,EAAE,EAAE;gBAAED,KAAK,EAAE;cAAe;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAChE9B,OAAA,CAACtD,UAAU;cAAC0I,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAC5D,KAAK,EAAC,cAAc;cAAA2C,QAAA,EAAC;YAE/D;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACb9B,OAAA,CAAC9C,KAAK;cAAC4I,QAAQ,EAAC,SAAS;cAACtE,EAAE,EAAE;gBAAE0C,QAAQ,EAAE;cAAI,CAAE;cAAAE,QAAA,gBAC9CpE,OAAA,CAAC7C,UAAU;gBAAAiH,QAAA,GAAC,eACG,EAAC,CAAA/D,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAE0F,cAAc,KAAI,YAAY;cAAA;gBAAApE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,mEAGf;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAER9B,OAAA,CAACpD,KAAK;cAACkI,SAAS,EAAC,KAAK;cAACC,OAAO,EAAE,CAAE;cAAAX,QAAA,gBAChCpE,OAAA,CAACrD,MAAM;gBACLyI,OAAO,EAAC,UAAU;gBAClBY,SAAS,eAAEhG,OAAA,CAAC7B,WAAW;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAC3BmE,OAAO,EAAEvD,cAAe;gBACxBwD,QAAQ,EAAEzF,UAAW;gBAAA2D,QAAA,EACtB;cAED;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9B,OAAA,CAACrD,MAAM;gBACLyI,OAAO,EAAC,WAAW;gBACnB3D,KAAK,EAAC,OAAO;gBACbwE,OAAO,EAAE7C,mBAAoB;gBAC7B8C,QAAQ,EAAEzF,UAAW;gBAAA2D,QAAA,EACtB;cAED;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT9B,OAAA,CAACrD,MAAM;gBACLyI,OAAO,EAAC,WAAW;gBACnBa,OAAO,EAAEA,CAAA,KAAM7F,QAAQ,CAAC,cAAcD,SAAS,EAAE,CAAE;gBAAAiE,QAAA,EACpD;cAED;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,gBAER9B,OAAA,CAACpD,KAAK;YAACmI,OAAO,EAAE,CAAE;YAACR,UAAU,EAAC,QAAQ;YAAAH,QAAA,gBACpCpE,OAAA,CAAC/B,SAAS;cAACuD,EAAE,EAAE;gBAAEE,QAAQ,EAAE,EAAE;gBAAED,KAAK,EAAE;cAAa;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxD9B,OAAA,CAACtD,UAAU;cAAC0I,OAAO,EAAC,IAAI;cAACC,UAAU,EAAE,GAAI;cAAAjB,QAAA,EAAC;YAE1C;cAAAzC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,EAEZrB,UAAU,iBACTT,OAAA,CAACzD,GAAG;cAACiF,EAAE,EAAE;gBAAEkC,KAAK,EAAE,MAAM;gBAAEQ,QAAQ,EAAE;cAAI,CAAE;cAAAE,QAAA,gBACxCpE,OAAA,CAAC5C,cAAc;gBACbgI,OAAO,EAAC,aAAa;gBACrBe,KAAK,EAAEpF,kBAAmB;gBAC1BS,EAAE,EAAE;kBAAE2D,MAAM,EAAE,CAAC;kBAAES,YAAY,EAAE,CAAC;kBAAEf,EAAE,EAAE;gBAAE;cAAE;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACF9B,OAAA,CAACtD,UAAU;gBAAC0I,OAAO,EAAC,OAAO;gBAAC3D,KAAK,EAAC,gBAAgB;gBAAA2C,QAAA,EAAC;cAEnD;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CACN,eAED9B,OAAA,CAACzD,GAAG;cAACiF,EAAE,EAAE;gBAAE4E,QAAQ,EAAE,UAAU;gBAAE/B,OAAO,EAAE;cAAe,CAAE;cAAAD,QAAA,gBACzDpE,OAAA,CAACzD,GAAG;gBACFyI,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAEpF,SAAU;gBACfqF,GAAG,EAAC,uBAAuB;gBAC3B1D,EAAE,EAAE;kBACF6E,MAAM,EAAE,SAAS;kBACjBlB,MAAM,EAAE,EAAE;kBACVzB,KAAK,EAAE,MAAM;kBACb4C,UAAU,EAAE,mBAAmB;kBAC/B,SAAS,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAC;kBACzBA,OAAO,EAAE9F,UAAU,GAAG,GAAG,GAAG,CAAC;kBAC7B+F,aAAa,EAAE/F,UAAU,GAAG,MAAM,GAAG;gBACvC,CAAE;gBACFwF,OAAO,EAAE,CAACxF,UAAU,GAAGkC,gBAAgB,GAAG8D;cAAU;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC,eACF9B,OAAA,CAACzD,GAAG;gBACFyI,SAAS,EAAC,KAAK;gBACfC,GAAG,EAAEnF,cAAe;gBACpBoF,GAAG,EAAC,uBAAuB;gBAC3B1D,EAAE,EAAE;kBACF4E,QAAQ,EAAE,UAAU;kBACpBM,GAAG,EAAE,CAAC;kBACNC,IAAI,EAAE,CAAC;kBACPxB,MAAM,EAAE,EAAE;kBACVzB,KAAK,EAAE,MAAM;kBACb6C,OAAO,EAAE,CAAC;kBACVD,UAAU,EAAE,mBAAmB;kBAC/B,SAAS,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAC;kBACzBC,aAAa,EAAE/F,UAAU,GAAG,MAAM,GAAG;gBACvC,CAAE;gBACFwF,OAAO,EAAE,CAACxF,UAAU,GAAGkC,gBAAgB,GAAG8D;cAAU;gBAAA9E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACR;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAGN,CAACvB,YAAY,iBACZP,OAAA,CAACvD,IAAI;UAAC+E,EAAE,EAAE;YAAEiE,CAAC,EAAE,CAAC;YAAEZ,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,gBACxBpE,OAAA,CAACtD,UAAU;YAAC0I,OAAO,EAAC,IAAI;YAACC,UAAU,EAAE,GAAI;YAACC,YAAY;YAAAlB,QAAA,EAAC;UAEvD;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb9B,OAAA,CAACtC,OAAO;YAACuD,UAAU,EAAEA,UAAW;YAAC2F,WAAW,EAAC,UAAU;YAAAxC,QAAA,EACpDjD,eAAe,CAAC0F,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC/B/G,OAAA,CAACrC,IAAI;cAAAyG,QAAA,gBACHpE,OAAA,CAACpC,SAAS;gBAAAwG,QAAA,EAAE0C,IAAI,CAAC1F;cAAK;gBAAAO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACnC9B,OAAA,CAACnC,WAAW;gBAAAuG,QAAA,eACVpE,OAAA,CAACtD,UAAU;kBAAC0I,OAAO,EAAC,OAAO;kBAAC3D,KAAK,EAAC,gBAAgB;kBAAA2C,QAAA,EAC/C0C,IAAI,CAACzF;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA,GANLiF,KAAK;cAAApF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAOV,CACP;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACP,eAGD9B,OAAA,CAACtD,UAAU;UACT0I,OAAO,EAAC,IAAI;UACZC,UAAU,EAAE,GAAI;UAChBT,SAAS,EAAC,QAAQ;UAClBpD,EAAE,EAAE;YAAEqD,EAAE,EAAE;UAAE,CAAE;UAAAT,QAAA,EACf;QAED;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEb9B,OAAA,CAAC3C,IAAI;UAAC2J,SAAS;UAACjC,OAAO,EAAE,CAAE;UAAAX,QAAA,EACxB9C,QAAQ,CAACuF,GAAG,CAAC,CAACI,OAAO,EAAEF,KAAK,kBAC3B/G,OAAA,CAAC3C,IAAI;YAAC6J,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAAjD,QAAA,eAC9BpE,OAAA,CAACvD,IAAI;cACH+E,EAAE,EAAE;gBACFiE,CAAC,EAAE,CAAC;gBACJN,MAAM,EAAE,MAAM;gBACdmB,UAAU,EAAE,2CAA2C;gBACvD,SAAS,EAAE;kBACTgB,SAAS,EAAE,kBAAkB;kBAC7BC,SAAS,EAAE;gBACb;cACF,CAAE;cAAAnD,QAAA,eAEFpE,OAAA,CAACpD,KAAK;gBAACmI,OAAO,EAAE,CAAE;gBAACR,UAAU,EAAC,QAAQ;gBAACK,SAAS,EAAC,QAAQ;gBAAAR,QAAA,GACtD6C,OAAO,CAAC1F,IAAI,eACbvB,OAAA,CAACtD,UAAU;kBAAC0I,OAAO,EAAC,IAAI;kBAACC,UAAU,EAAE,GAAI;kBAAAjB,QAAA,EACtC6C,OAAO,CAAClF;gBAAK;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ,CAAC,eACb9B,OAAA,CAACtD,UAAU;kBAAC0I,OAAO,EAAC,OAAO;kBAAC3D,KAAK,EAAC,gBAAgB;kBAAA2C,QAAA,EAC/C6C,OAAO,CAAC5F;gBAAW;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC,GArB6BiF,KAAK;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBrC,CACP;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGP9B,OAAA,CAACvD,IAAI;UAAC+E,EAAE,EAAE;YAAEiE,CAAC,EAAE,CAAC;YAAE+B,EAAE,EAAE,CAAC;YAAEC,eAAe,EAAE;UAAU,CAAE;UAAArD,QAAA,eACpDpE,OAAA,CAACpD,KAAK;YAACkI,SAAS,EAAC,KAAK;YAACC,OAAO,EAAE,CAAE;YAACR,UAAU,EAAC,QAAQ;YAAAH,QAAA,gBACpDpE,OAAA,CAACb,UAAU;cAACqC,EAAE,EAAE;gBAAEC,KAAK,EAAE,cAAc;gBAAEC,QAAQ,EAAE;cAAG;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3D9B,OAAA,CAACzD,GAAG;cAAA6H,QAAA,gBACFpE,OAAA,CAACtD,UAAU;gBAAC0I,OAAO,EAAC,IAAI;gBAACC,UAAU,EAAE,GAAI;gBAACC,YAAY;gBAAAlB,QAAA,EAAC;cAEvD;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC,eACb9B,OAAA,CAACtD,UAAU;gBAAC0I,OAAO,EAAC,OAAO;gBAAC3D,KAAK,EAAC,gBAAgB;gBAAA2C,QAAA,EAAC;cAInD;gBAAAzC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEhB,CAAC;AAAC5B,EAAA,CA5bID,iBAAiB;EAAA,QACCX,SAAS,EACdC,WAAW;AAAA;AAAAmI,EAAA,GAFxBzH,iBAAiB;AA8bvB,eAAeA,iBAAiB;AAAC,IAAAyH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}