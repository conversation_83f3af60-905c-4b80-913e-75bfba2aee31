{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Valisights\\\\valisight-app\\\\src\\\\App.js\";\nimport React from \"react\";\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\nimport Auth from \"./pages/Auth/Auth\";\nimport Login from \"./pages/Auth/Components/Login\";\nimport ForgotPassword from \"./pages/Auth/Components/ForgotPassword\";\nimport CheckEmail from \"./pages/Auth/Components/CheckEmail\";\nimport ResetPassword from \"./pages/Auth/Components/ResetPassword\";\nimport Dashboard from \"./pages/Dashboard/Dashboard\";\nimport CompanyDetail from \"./pages/Companies/Components/View\";\nimport Examples from \"./pages/examples/Examples\";\nimport Layout from \"./layout/LayOut\";\nimport Faqs from \"./pages/faqs/Faqs\";\nimport EditReportPage from \"./pages/reports/EditReportPage\";\nimport ResetLinkPwd from \"./pages/Auth/Components/ResetLink\";\nimport QboCallback from \"./pages/QboCallback/qboCallback\";\n// import Report from \"./pages/Report/Report\";\nimport SharedCompanies from \"./pages/Companies/shared-companies/SharedCompanies\";\nimport CustomizeReport from \"./pages/reports/CustomizeReport\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst router = createBrowserRouter([{\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(Auth, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 14\n  }, this),\n  children: [{\n    index: true,\n    element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"login\",\n    element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"forgot-password\",\n    element: /*#__PURE__*/_jsxDEV(ForgotPassword, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 35,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"check-email\",\n    element: /*#__PURE__*/_jsxDEV(CheckEmail, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"reset-password\",\n    element: /*#__PURE__*/_jsxDEV(ResetPassword, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"reset-link\",\n    element: /*#__PURE__*/_jsxDEV(ResetLinkPwd, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 18\n    }, this)\n  }]\n}, {\n  path: \"/\",\n  element: /*#__PURE__*/_jsxDEV(Layout, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 14\n  }, this),\n  children: [{\n    path: \"dashboard\",\n    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"companies\",\n    element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"share-companies\",\n    element: /*#__PURE__*/_jsxDEV(SharedCompanies, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"company/:id\",\n    element: /*#__PURE__*/_jsxDEV(CompanyDetail, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"qbo-callback\",\n    element: /*#__PURE__*/_jsxDEV(QboCallback, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"examples\",\n    element: /*#__PURE__*/_jsxDEV(Examples, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"faqs\",\n    element: /*#__PURE__*/_jsxDEV(Faqs, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 18\n    }, this)\n  }, {\n    path: \"companies/:companyId/reports/:reportId/edit\",\n    element: /*#__PURE__*/_jsxDEV(EditReportPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 18\n    }, this)\n  }]\n}, {\n  path: \"/\",\n  children: [{\n    path: \"company/:id/custom-template/:reportId?\",\n    element: /*#__PURE__*/_jsxDEV(CustomizeReport, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 18\n    }, this)\n  }]\n}]);\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(RouterProvider, {\n    router: router\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 101,\n    columnNumber: 10\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "createBrowserRouter", "RouterProvider", "<PERSON><PERSON>", "<PERSON><PERSON>", "ForgotPassword", "CheckEmail", "ResetPassword", "Dashboard", "CompanyDetail", "Examples", "Layout", "Faqs", "EditReportPage", "ResetLinkPwd", "QboCallback", "SharedCompanies", "CustomizeReport", "jsxDEV", "_jsxDEV", "router", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "children", "index", "App", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Valisights/valisight-app/src/App.js"], "sourcesContent": ["import React from \"react\";\r\nimport { createBrowserRouter, RouterProvider } from \"react-router-dom\";\r\nimport Auth from \"./pages/Auth/Auth\";\r\nimport Login from \"./pages/Auth/Components/Login\";\r\nimport ForgotPassword from \"./pages/Auth/Components/ForgotPassword\";\r\nimport CheckEmail from \"./pages/Auth/Components/CheckEmail\";\r\nimport ResetPassword from \"./pages/Auth/Components/ResetPassword\";\r\nimport Dashboard from \"./pages/Dashboard/Dashboard\";\r\nimport CompanyDetail from \"./pages/Companies/Components/View\";\r\nimport Examples from \"./pages/examples/Examples\";\r\nimport Layout from \"./layout/LayOut\";\r\nimport Faqs from \"./pages/faqs/Faqs\";\r\nimport EditReportPage from \"./pages/reports/EditReportPage\";\r\nimport ResetLinkPwd from \"./pages/Auth/Components/ResetLink\";\r\nimport QboCallback from \"./pages/QboCallback/qboCallback\";\r\n// import Report from \"./pages/Report/Report\";\r\nimport SharedCompanies from \"./pages/Companies/shared-companies/SharedCompanies\"\r\nimport CustomizeReport from \"./pages/reports/CustomizeReport\";\r\n\r\nconst router = createBrowserRouter([\r\n  {\r\n    path: \"/\",\r\n    element: <Auth />,\r\n    children: [\r\n      {\r\n        index: true,\r\n        element: <Login />,\r\n      },\r\n      {\r\n        path: \"login\",\r\n        element: <Login />,\r\n      },\r\n      {\r\n        path: \"forgot-password\",\r\n        element: <ForgotPassword />,\r\n      },\r\n      {\r\n        path: \"check-email\",\r\n        element: <CheckEmail />,\r\n      },\r\n      {\r\n        path: \"reset-password\",\r\n        element: <ResetPassword />,\r\n      },\r\n      {\r\n        path: \"reset-link\",\r\n        element: <ResetLinkPwd />,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/\",\r\n    element: <Layout />,\r\n    children: [\r\n      {\r\n        path: \"dashboard\",\r\n        element: <Dashboard />,\r\n      },\r\n      {\r\n        path: \"companies\",\r\n        element: <Dashboard />,\r\n      },\r\n      {\r\n        path: \"share-companies\",\r\n        element: <SharedCompanies />,\r\n      },\r\n      {\r\n        path: \"company/:id\",\r\n        element: <CompanyDetail />,\r\n      },\r\n      {\r\n        path: \"qbo-callback\",\r\n        element: <QboCallback />,\r\n      },\r\n      {\r\n        path: \"examples\",\r\n        element: <Examples />,\r\n      },\r\n      {\r\n        path: \"faqs\",\r\n        element: <Faqs />,\r\n      },\r\n      {\r\n        path: \"companies/:companyId/reports/:reportId/edit\",\r\n        element: <EditReportPage />,\r\n      },\r\n    ],\r\n  },\r\n  {\r\n    path: \"/\",\r\n    children: [\r\n     {\r\n        path: \"company/:id/custom-template/:reportId?\",\r\n        element: <CustomizeReport />,\r\n      },\r\n    ],\r\n  },\r\n]);\r\n\r\nfunction App() {\r\n  return <RouterProvider router={router} />;\r\n}\r\n\r\nexport default App;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,mBAAmB,EAAEC,cAAc,QAAQ,kBAAkB;AACtE,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,KAAK,MAAM,+BAA+B;AACjD,OAAOC,cAAc,MAAM,wCAAwC;AACnE,OAAOC,UAAU,MAAM,oCAAoC;AAC3D,OAAOC,aAAa,MAAM,uCAAuC;AACjE,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,MAAM,MAAM,iBAAiB;AACpC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,cAAc,MAAM,gCAAgC;AAC3D,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,WAAW,MAAM,iCAAiC;AACzD;AACA,OAAOC,eAAe,MAAM,oDAAoD;AAChF,OAAOC,eAAe,MAAM,iCAAiC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9D,MAAMC,MAAM,GAAGnB,mBAAmB,CAAC,CACjC;EACEoB,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAAChB,IAAI;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACjBC,QAAQ,EAAE,CACR;IACEC,KAAK,EAAE,IAAI;IACXN,OAAO,eAAEH,OAAA,CAACf,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB,CAAC,EACD;IACEL,IAAI,EAAE,OAAO;IACbC,OAAO,eAAEH,OAAA,CAACf,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACnB,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACd,cAAc;MAAAkB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5B,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACb,UAAU;MAAAiB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACxB,CAAC,EACD;IACEL,IAAI,EAAE,gBAAgB;IACtBC,OAAO,eAAEH,OAAA,CAACZ,aAAa;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC3B,CAAC,EACD;IACEL,IAAI,EAAE,YAAY;IAClBC,OAAO,eAAEH,OAAA,CAACL,YAAY;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC1B,CAAC;AAEL,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTC,OAAO,eAAEH,OAAA,CAACR,MAAM;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACnBC,QAAQ,EAAE,CACR;IACEN,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACX,SAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACvB,CAAC,EACD;IACEL,IAAI,EAAE,WAAW;IACjBC,OAAO,eAAEH,OAAA,CAACX,SAAS;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACvB,CAAC,EACD;IACEL,IAAI,EAAE,iBAAiB;IACvBC,OAAO,eAAEH,OAAA,CAACH,eAAe;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC,EACD;IACEL,IAAI,EAAE,aAAa;IACnBC,OAAO,eAAEH,OAAA,CAACV,aAAa;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC3B,CAAC,EACD;IACEL,IAAI,EAAE,cAAc;IACpBC,OAAO,eAAEH,OAAA,CAACJ,WAAW;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACzB,CAAC,EACD;IACEL,IAAI,EAAE,UAAU;IAChBC,OAAO,eAAEH,OAAA,CAACT,QAAQ;MAAAa,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACtB,CAAC,EACD;IACEL,IAAI,EAAE,MAAM;IACZC,OAAO,eAAEH,OAAA,CAACP,IAAI;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAClB,CAAC,EACD;IACEL,IAAI,EAAE,6CAA6C;IACnDC,OAAO,eAAEH,OAAA,CAACN,cAAc;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC5B,CAAC;AAEL,CAAC,EACD;EACEL,IAAI,EAAE,GAAG;EACTM,QAAQ,EAAE,CACT;IACGN,IAAI,EAAE,wCAAwC;IAC9CC,OAAO,eAAEH,OAAA,CAACF,eAAe;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EAC7B,CAAC;AAEL,CAAC,CACF,CAAC;AAEF,SAASG,GAAGA,CAAA,EAAG;EACb,oBAAOV,OAAA,CAACjB,cAAc;IAACkB,MAAM,EAAEA;EAAO;IAAAG,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC3C;AAACI,EAAA,GAFQD,GAAG;AAIZ,eAAeA,GAAG;AAAC,IAAAC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}